<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',<PERSON><PERSON>,sans-serif;">钱包地址持仓统计</h2>

    <div style="margin: 20px 0;">

      <el-form :inline="true" class="filter-form">
        <el-form-item label="公链">
          <el-select v-model="filter.chain" clearable placeholder="<ALL>" style="width: 120px;" @change="handleLimitChange">
            <el-option v-for="v in chains" :key="v" :label="v" :value="v">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="合约地址">
          <el-input v-model="filter.contract" placeholder="请输入" style="width: 600px;" @input="handleLimitChange"/>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="show_items" border style="width: 100%;">
      <el-table-column prop="symbol" label="symbol" width="100px"></el-table-column>
      <el-table-column prop="name" label="name" width="200px"></el-table-column>
      <el-table-column prop="chain" label="公链" width="100px"></el-table-column>
      <el-table-column prop="contract" label="合约地址"></el-table-column>
      <el-table-column prop="amount" label="持有数量" width="150px"></el-table-column>
      <el-table-column prop="volume" label="持有价值(USD)" width="150px"></el-table-column>
    </el-table>

    <div style="margin-top: 20px; text-align: right;">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="filter_items.length"
        :page-sizes="[10, 50, 100, 200]"
        :page-size="limit"
        :current-page.sync="page"
        @size-change="handleLimitChange"
        @current-change="pageChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>

export default {
  data() {
    return {
      filter: {
        chain: null,
        contract: null,
      },
      chains: [],
      limit: 50,
      page: 1,
      items: [],
      filter_items: [],
      show_items: [],
    };
  },
  methods: {
    getData() {
      this.loading = true;
      this.$axios.get(`/api/onchain/statistic/wallet-assets`, {params: this.filter}).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.chains = data.chains;
          this.handleLimitChange();
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    filteredData() {
      this.filter_items = this.items;
      if (this.filter.chain !== null && this.filter.chain !== '') {
        this.filter_items = this.filter_items.filter(item => item.chain.toLowerCase() === this.filter.chain.toLowerCase());
      }
      if (this.filter.contract !== null && this.filter.contract !== '') {
        this.filter_items = this.filter_items.filter(item => item.contract.toLowerCase() === this.filter.contract.toLowerCase());
      }
    },
    pageChange() {
      const start = (this.page - 1) * this.limit;
      const end = start + this.limit;
      this.filteredData();
      this.show_items = this.filter_items.slice(start, end);
    },
    handleLimitChange() {
      this.page = 1;
      this.pageChange();
    },
  },
  mounted() {
    this.getData();
  },
};
</script>
